<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 主色调 - 现代化深蓝色系 -->
    <Color x:Key="PrimaryColor">#FF1565C0</Color>
    <Color x:Key="PrimaryDarkColor">#FF0D47A1</Color>
    <Color x:Key="PrimaryLightColor">#FF42A5F5</Color>

    <!-- 辅助色调 - 鲜艳橙色系 -->
    <Color x:Key="AccentColor">#FFFF6F00</Color>
    <Color x:Key="AccentDarkColor">#FFE65100</Color>
    <Color x:Key="AccentLightColor">#FFFF9800</Color>

    <!-- 状态颜色 - 高对比度 -->
    <Color x:Key="SuccessColor">#FF00C853</Color>
    <Color x:Key="WarningColor">#FFFFC107</Color>
    <Color x:Key="ErrorColor">#FFD32F2F</Color>
    <Color x:Key="InfoColor">#FF1976D2</Color>

    <!-- 高级渐变色 -->
    <Color x:Key="GradientStart">#FF667EEA</Color>
    <Color x:Key="GradientEnd">#FF764BA2</Color>
    <Color x:Key="CardGradientStart">#FFFFFFFF</Color>
    <Color x:Key="CardGradientEnd">#FFF8F9FA</Color>
    
    <!-- 中性色 - 现代化背景 -->
    <Color x:Key="BackgroundColor">#FFF5F7FA</Color>
    <Color x:Key="SurfaceColor">#FFFFFFFF</Color>
    <Color x:Key="BorderColor">#FFE1E5E9</Color>
    <Color x:Key="DividerColor">#FFCFD8DC</Color>
    <Color x:Key="HoverColor">#FFF0F4F8</Color>
    <Color x:Key="ActiveColor">#FFE3F2FD</Color>

    <!-- 文本颜色 - 高对比度 -->
    <Color x:Key="TextPrimaryColor">#FF263238</Color>
    <Color x:Key="TextSecondaryColor">#FF546E7A</Color>
    <Color x:Key="TextDisabledColor">#FFBDBDBD</Color>
    <Color x:Key="TextOnPrimaryColor">#FFFFFFFF</Color>
    <Color x:Key="TextOnAccentColor">#FFFFFFFF</Color>
    
    <!-- 数据网格颜色 -->
    <Color x:Key="GridHeaderColor">#FFF5F5F5</Color>
    <Color x:Key="GridAlternateRowColor">#FFFAFAFA</Color>
    <Color x:Key="GridSelectedRowColor">#FFE3F2FD</Color>
    <Color x:Key="GridHoverRowColor">#FFF0F0F0</Color>
    
    <!-- 原料状态颜色 -->
    <Color x:Key="MaterialNormalColor">#FF4CAF50</Color>
    <Color x:Key="MaterialWarningColor">#FFFF9800</Color>
    <Color x:Key="MaterialErrorColor">#FFF44336</Color>
    <Color x:Key="MaterialEditingColor">#FF2196F3</Color>
    <Color x:Key="MaterialLockedColor">#FF9E9E9E</Color>

    <!-- 画刷定义 -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="AccentDarkBrush" Color="{StaticResource AccentDarkColor}"/>
    <SolidColorBrush x:Key="AccentLightBrush" Color="{StaticResource AccentLightColor}"/>
    
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>
    
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="{StaticResource TextPrimaryColor}"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="{StaticResource TextSecondaryColor}"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="{StaticResource TextDisabledColor}"/>
    <SolidColorBrush x:Key="TextOnPrimaryBrush" Color="{StaticResource TextOnPrimaryColor}"/>
    
    <SolidColorBrush x:Key="GridHeaderBrush" Color="{StaticResource GridHeaderColor}"/>
    <SolidColorBrush x:Key="GridAlternateRowBrush" Color="{StaticResource GridAlternateRowColor}"/>
    <SolidColorBrush x:Key="GridSelectedRowBrush" Color="{StaticResource GridSelectedRowColor}"/>
    <SolidColorBrush x:Key="GridHoverRowBrush" Color="{StaticResource GridHoverRowColor}"/>
    
    <SolidColorBrush x:Key="MaterialNormalBrush" Color="{StaticResource MaterialNormalColor}"/>
    <SolidColorBrush x:Key="MaterialWarningBrush" Color="{StaticResource MaterialWarningColor}"/>
    <SolidColorBrush x:Key="MaterialErrorBrush" Color="{StaticResource MaterialErrorColor}"/>
    <SolidColorBrush x:Key="MaterialEditingBrush" Color="{StaticResource MaterialEditingColor}"/>
    <SolidColorBrush x:Key="MaterialLockedBrush" Color="{StaticResource MaterialLockedColor}"/>

    <!-- 高级渐变画刷 -->
    <LinearGradientBrush x:Key="HeaderGradientBrush" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="{StaticResource GradientStart}" Offset="0"/>
        <GradientStop Color="{StaticResource GradientEnd}" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="CardGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource CardGradientStart}" Offset="0"/>
        <GradientStop Color="{StaticResource CardGradientEnd}" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ButtonGradientBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientStop Color="{StaticResource AccentColor}" Offset="0"/>
        <GradientStop Color="{StaticResource AccentDarkColor}" Offset="1"/>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="PrimaryGradientBrush" StartPoint="0,0" EndPoint="1,0">
        <GradientStop Color="{StaticResource PrimaryColor}" Offset="0"/>
        <GradientStop Color="{StaticResource PrimaryLightColor}" Offset="1"/>
    </LinearGradientBrush>

    <!-- 阴影效果 -->
    <DropShadowEffect x:Key="CardShadow" 
                      Color="#40000000" 
                      Direction="270" 
                      ShadowDepth="2" 
                      BlurRadius="8" 
                      Opacity="0.3"/>
    
    <DropShadowEffect x:Key="ButtonShadow" 
                      Color="#30000000" 
                      Direction="270" 
                      ShadowDepth="1" 
                      BlurRadius="4" 
                      Opacity="0.2"/>

</ResourceDictionary>
