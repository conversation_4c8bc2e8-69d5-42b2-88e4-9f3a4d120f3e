# 烧结配料系统客户端前端界面技术文档（基于界面原型深度解析）

## 一、界面整体布局与区域划分

### 1.1 布局框架（像素级还原）

### 1.1 布局框架（像素级还原）

整体采用响应式布局，以 1920×1080 分辨率为基准设计，确保不同屏幕尺寸下的适配性。界面分为**左侧导航栏**、**顶部操作栏**、**主内容区**和**底部操作汇总栏**四部分：

```mermaid
flowchart TD
    classDef area fill:#F5F7FA,stroke:#ccc,stroke-width:1px;
    classDef subarea fill:#FFFFFF,stroke:#ccc,stroke-width:1px;

    A([1920×1080 界面]):::area -->|左侧 240px| B[左侧导航栏<br>背景色 #F5F7FA<br>高度 100%<br>底部距底 20px]:::subarea
    A -->|顶部 40px| C[顶部操作栏<br>背景色 #FFFFFF<br>含搜索框、头像等]:::subarea
    A -->|剩余横向空间| D[主内容区<br>栅格布局<br>多折叠面板]:::subarea
    A -->|底部 70px| E[底部操作汇总栏<br>背景色 #F9FAFB<br>居中按钮]:::subarea

    C -->|右侧边缘对齐| D
    B -->|右侧衔接| D
    D -->|底部相接| E
```

* **左侧导航栏**：占据页面左侧垂直方向 100% 高度，宽度固定为 240px，背景色采用 #F5F7FA。内容包含 logo、菜单列表（图标 + 文字），菜单高度随内容动态调整，底部距离页面底部保持 20px 间距，实现无缝覆盖左侧区域。
* **顶部操作栏**：横向贯穿页面顶部，高度 40px，背景色 #FFFFFF，包含搜索框、用户头像、消息通知等功能模块，右侧边缘与主内容区对齐。
* **主内容区**：占据页面剩余横向空间，左侧与导航栏紧密衔接，右侧延伸至页面右边缘，顶部与操作栏底部对齐，底部与底部操作汇总栏顶部相接。内部分为多个可折叠面板，采用栅格布局实现动态内容展示 。
* **底部操作汇总栏**：横向贯穿页面底部，高度 70px，背景色 #F9FAFB，用于集中展示「保存」「提交」「重置」等全局操作按钮，按钮采用图标 + 文字组合，居中对齐，两侧边缘与主内容区对齐，方便用户快速执行关键操作。

## 二、左侧导航栏细节设计

### 2.1 结构与交互

| 模块               | 布局细节                                                                                                                       | 交互规则                                                                                                                                      |
| ------------------ | ------------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------------------- |
| **菜单树**   | - 层级：3 级（如 `原料管理 > 铁矿类 > 澳矿块`）- 图标：文件夹（父级）/ 文件（子级，16×16px）- 行高：30px，文字 12px（#333） | - 点击父级：折叠 / 展开子菜单（动画时长 200ms）- 选中子级：背景色 #1E88E5（蓝），文字 #FFF- 右键菜单：`刷新数据`/`批量导入`（仅父级可见） |
| **快捷入口** | - 位置：菜单树底部，2 行按钮（`方案管理`/`历史记录`）- 按钮尺寸：180px×36px，圆角 4px                                     | - 点击：跳转至对应页面，按钮高亮（边框 #1976D2）- hover：背景色 #F5F5F5                                                                       |

## 三、主数据表格区深度解析

### 3.1 列结构与功能（按界面原型列顺序）

| 列索引 | 列名        | 宽度  | 数据类型 | 显示规则                                                                                   | 交互设计                                                            |
| ------ | ----------- | ----- | -------- | ------------------------------------------------------------------------------------------ | ------------------------------------------------------------------- |
| 1      | 序号        | 50px  | 数字     | 居中，自动递增（`1,2,3...`）                                                             | 双击：定位至对应原料的编辑弹窗                                      |
| 2      | 代码        | 80px  | 字符串   | 左对齐，如 `HK01`，tooltip 显示完整编码规则                                              | 编辑：双击进入文本框模式（限制 6 字符）                             |
| 3      | 名称        | 120px | 字符串   | 左对齐，如 `澳矿块`，不同原料类型标色（铁矿 #2196F3 / 熔剂 #4CAF50）                     | 右键：`复制名称`/`关联文档`（弹出 PDF 预览）                    |
| 4      | TFe（湿基） | 80px  | 百分比   | - 正常范围（如 55\~65%）：背景 #E8F5E9（浅绿）- 超标：背景 #FFEBEE（浅红）- 精度：1 位小数 | 编辑：滑块 + 输入框联动（滑块范围 0\~100%，步长 0.1）               |
| 5      | SiO₂       | 80px  | 百分比   | 同 TFe 规则，目标范围 4\~6%（示例）                                                        | 联动计算：修改后自动更新底部 `混合料SiO₂` 汇总值                 |
| 6      | CaO         | 80px  | 百分比   | 目标范围 8\~12%，背景色规则同上                                                            | 关键列：参与碱度（CaO/SiO₂）计算，标蓝边框                         |
| ...    | ...         | ...   | ...      | ...                                                                                        | ...                                                                 |
| N-2    | 配比        | 80px  | 百分比   | - 编辑中：背景 #E3F2FD（浅蓝）- 锁定值：背景 #F5F5F5（灰），带锁图标                       | 批量修改：框选多行后，右侧弹出 `统一设置配比` 输入框              |
| N-1    | 烧损        | 60px  | 百分比   | 固定值（如 12.5%），灰色文字（不可编辑）                                                   | tooltip：显示烧损计算公式 `烧损 = (湿重 - 干重)/湿重 ×100%`      |
| N      | 操作        | 100px | 按钮组   | 2 按钮：`编辑`（铅笔图标）/`删除`（垃圾桶图标）                                        | 编辑按钮：跳转至单行详细编辑弹窗；删除按钮：二次确认（弹窗 + 音效） |

### 3.2 行状态与颜色编码（核心规则）

| 状态类型         | 视觉标记                                                     | 触发条件                               |
| ---------------- | ------------------------------------------------------------ | -------------------------------------- |
| **正常行** | 背景 #FFFFFF，文字 #333                                      | 所有字段在目标范围内                   |
| **警告行** | 背景 #FFF8E1（浅黄），文字 #FF9800（橙）                     | 单字段超标（如 TFe < 55%）             |
| **错误行** | 背景 #FFEBEE（浅红），文字 #F44336（红）                     | 多字段超标（如 TFe <55% 且 SiO₂> 6%） |
| **编辑行** | 背景 #E3F2FD（浅蓝），边框 2px #1976D2（蓝）                 | 双击进入编辑模式，或框选后批量编辑     |
| **锁定行** | 背景 #F5F5F5（灰），文字 #9E9E9E（灰），带锁图标（16×16px） | 系统内置原料（不可修改配比 / 成分）    |

## 四、底部操作汇总栏设计

### 4.1 区域划分（左→右）

```
┌─────────────────────────────────────────────────────────────┐  

│ 左侧：混合料成分汇总（背景 #F5F5F5，内边距 10px）            │  

├─────────────────────────────────────────────────────────────┤  

│ 中间：功能按钮区（5 按钮，间距 10px）                       │  

├─────────────────────────────────────────────────────────────┤  

│ 右侧：状态显示区（时间、报警、计算状态）                     │  

└─────────────────────────────────────────────────────────────┘  
```

### 4.2 各模块细节

#### （1）混合料成分汇总（左侧）

| 指标       | 显示规则                                                 | 交互设计                                           |
| ---------- | -------------------------------------------------------- | -------------------------------------------------- |
| TFe 合计   | 字体 14px（#2196F3 蓝），格式 `58.2%`（保留 1 位小数） | 点击：展开计算公式弹窗 `Σ(配比×TFe)/Σ配比`    |
| SiO₂ 合计 | 字体 14px（#4CAF50 绿），格式 `5.1%`                   | 联动更新：任意原料 SiO₂ 修改后，300ms 内自动刷新  |
| 碱度（Ro） | 字体 16px（#FF9800 橙），格式 `1.85`（CaO/ SiO₂）     | 预警：Ro < 1.8 时，文字闪烁（红→橙→红，周期 1s） |

#### （2）功能按钮区（中间）

| 按钮名称   | 图标（16×16px） | 尺寸      | 交互反馈                                                                 | 功能逻辑                                       |
| ---------- | ---------------- | --------- | ------------------------------------------------------------------------ | ---------------------------------------------- |
| 计算配比   | 🔄（刷新）       | 100×36px | 点击时按钮抖动（200ms），显示加载转圈（右侧，24×24px）                  | 调用后端 SQP 算法，弹窗显示计算进度（0\~100%） |
| 保存方案   | 💾（保存）       | 100×36px | hover 时背景 #E0E0E0，点击后状态栏提示 “方案已保存（ID:20250730\_01）” | 存储至本地数据库 + 同步云端                    |
| 导出 Excel | 📥（下载）       | 120×36px | 点击后弹出路径选择框（默认 `C:\Sinter\Export\`）                       | 导出当前表格数据（含隐藏列），格式 `.xlsx`   |
| 打印       | 🖨️（打印）     | 100×36px | 弹出打印预览窗口（支持自定义列、页眉）                                   | 调用系统打印服务，默认横向布局                 |
| 高级设置   | ⚙️（设置）     | 100×36px | 点击后弹出抽屉式设置面板（右侧滑出，占 50% 宽度）                        | 配置算法参数（迭代次数、权重）、界面颜色       |

#### （3）状态显示区（右侧）

| 元素     | 显示规则                                                     | 交互设计                                                   |
| -------- | ------------------------------------------------------------ | ---------------------------------------------------------- |
| 系统时间 | 字体 12px（#666），格式 `2025-07-30 14:23:45`              | 点击：同步系统时间（自动校准）                             |
| 报警提示 | 红色数字（如 `3`）+ 铃铛图标，闪烁（红→灰→红）           | 点击：弹出报警列表（`TFe 超标：3 条`/`Ro 过低：1 条`） |
| 计算状态 | 绿色对勾（计算完成）/ 黄色转圈（计算中）/ 红色叉（计算失败） | hover：显示失败原因（如 `原料 A 配比超出范围`）          |

## 五、界面交互增强逻辑（贴合工业场景）

### 5.1 批量操作流程

1. **框选数据**：

* 鼠标拖动选择多行，选中行显示蓝色边框（2px #1976D2），底部显示 `已选中 8 行` 提示。

1. **批量编辑**：

* 右键菜单选择 `统一修改 TFe`，弹出输入框（范围 0\~100%），修改后所有选中行 TFe 同步更新。

1. **批量删除**：

* 右键菜单选择 `删除选中行`，二次确认弹窗（含选中行名称列表），删除后自动更新序号。

### 5.2 实时计算联动

1. **成分修改**：

* 任意原料的 TFe/SiO₂/CaO 修改后，300ms 内自动更新 **底部混合料合计值** 和 **碱度（Ro）**。

1. **配比修改**：

* 单原料配比修改后，自动触发 **同类型原料配比再平衡**（如铁矿类总配比保持 60±5%）。

### 5.3 报警与提示体系

| 报警类型 | 触发条件              | 提示方式                                                         |
| -------- | --------------------- | ---------------------------------------------------------------- |
| 成分超标 | TFe <55% 或 SiO₂> 6% | 行背景变红，底部报警数 +1，播放 500ms 蜂鸣音                     |
| 碱度异常 | Ro <1.8 或 Ro> 2.2    | 碱度数值闪烁（红 / 橙），右侧显示建议（`增加石灰石配比`）      |
| 计算失败 | 算法无可行解          | 红色弹窗（`低 TFe + 低 Ro，建议调整原料`），附带原料调整建议表 |

**备注**：需结合实际界面截图，对每个列、按钮、颜色标记进行像素级校验，确保与文档描述完全一致。
