# -*- coding: utf-8 -*-
"""
测试优化算法
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

try:
    from optimization_service import SinterOptimizer, OptimizationType, DEFAULT_MATERIALS
    import numpy as np
    
    print("🔥 开始测试优化算法...")
    
    # 创建优化器
    optimizer = SinterOptimizer()
    optimizer.load_materials(DEFAULT_MATERIALS)
    
    print(f"📋 已加载 {len(optimizer.materials)} 种原料")
    
    # 测试计算烧结矿性质
    test_ratios = np.array([20, 15, 10, 20, 5, 15, 10, 3, 2, 0, 0, 0, 0, 0])
    props = optimizer.calculate_sinter_properties(test_ratios)
    
    print(f"🧪 测试配比计算结果:")
    print(f"   TFe: {props[0]:.2f}%")
    print(f"   碱度: {props[1]:.2f}")
    print(f"   MgO: {props[2]:.2f}%")
    print(f"   Al2O3: {props[3]:.2f}%")
    print(f"   成本: {props[4]:.2f}元/吨")
    
    # 测试优化
    print("\n🚀 开始优化测试...")
    result = optimizer.optimize(OptimizationType.COST_OPTIMAL, max_iterations=3)
    
    if result.success:
        print("✅ 优化成功!")
        print(f"📊 优化结果:")
        for name, ratio in result.ratios.items():
            print(f"   {name}: {ratio:.2f}%")
        print(f"🎯 性质指标:")
        for prop, value in result.properties.items():
            print(f"   {prop}: {value:.2f}")
    else:
        print(f"❌ 优化失败: {result.message}")
        
except Exception as e:
    print(f"💥 测试异常: {e}")
    import traceback
    traceback.print_exc()
