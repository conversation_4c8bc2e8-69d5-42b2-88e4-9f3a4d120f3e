<Application x:Class="SinterBlendingSystem.WPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:SinterBlendingSystem.WPF.Converters"
             DispatcherUnhandledException="Application_DispatcherUnhandledException">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Dark" PrimaryColor="BlueGrey" SecondaryColor="Lime" />
                
                <!-- 自定义样式 -->
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/CommonStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/DataGridStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/ButtonStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 全局字体设置 -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="FontFamily" Value="Microsoft YaHei UI" />
                <Setter Property="FontSize" Value="12" />
            </Style>

            <Style TargetType="{x:Type UserControl}">
                <Setter Property="FontFamily" Value="Microsoft YaHei UI" />
                <Setter Property="FontSize" Value="12" />
            </Style>

            <!-- 应用程序级别的转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

            <!-- 自定义转换器 -->
            <local:StatusToColorConverter x:Key="StatusToColorConverter" />
            <local:StatusToBrushConverter x:Key="StatusToBrushConverter" />
            <local:EnumToBooleanConverter x:Key="EnumToBooleanConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
