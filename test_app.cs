using System;
using System.Windows;
using System.Windows.Threading;

namespace TestApp
{
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Console.WriteLine("正在启动WPF应用程序...");
                
                var app = new Application();
                app.DispatcherUnhandledException += (sender, e) =>
                {
                    Console.WriteLine($"未处理的异常: {e.Exception}");
                    e.Handled = true;
                };
                
                var window = new Window
                {
                    Title = "测试窗口",
                    Width = 400,
                    Height = 300,
                    Content = new System.Windows.Controls.TextBlock
                    {
                        Text = "WPF应用程序启动成功！",
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        FontSize = 16
                    }
                };
                
                app.Run(window);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动失败: {ex}");
                Console.WriteLine($"异常类型: {ex.GetType().Name}");
                Console.WriteLine($"异常消息: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
} 