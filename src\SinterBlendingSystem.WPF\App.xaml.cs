using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SinterBlendingSystem.Core.Interfaces;
using SinterBlendingSystem.Core.Services;
using SinterBlendingSystem.WPF.ViewModels;
using System;
using System.Windows;

namespace SinterBlendingSystem.WPF
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                Console.WriteLine("正在启动烧结配料系统...");
                
                // 配置依赖注入容器
                _host = Host.CreateDefaultBuilder()
                    .ConfigureServices((context, services) =>
                    {
                        Console.WriteLine("正在配置服务...");
                        
                        // 注册服务
                        services.AddHttpClient();
                        services.AddLogging(builder =>
                        {
                            builder.AddConsole();
                            builder.AddDebug();
                            builder.SetMinimumLevel(LogLevel.Information);
                        });

                        // 注册核心服务
                        services.AddSingleton<IDataService, DataService>();
                        services.AddSingleton<IOptimizationService, OptimizationService>();

                        // 注册ViewModels
                        services.AddTransient<MainWindowViewModel>();
                        services.AddTransient<MaterialListViewModel>();
                        services.AddTransient<OptimizationConfigViewModel>();
                        services.AddTransient<ResultDisplayViewModel>();

                        // 注册主窗口
                        services.AddSingleton<MainWindow>();
                        
                        Console.WriteLine("服务配置完成");
                    })
                    .Build();

                Console.WriteLine("正在创建主窗口...");
                
                // 启动主窗口
                var mainWindow = _host.Services.GetRequiredService<MainWindow>();
                mainWindow.Show();
                
                Console.WriteLine("主窗口已显示");

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"应用程序启动失败：{ex.Message}");
                Console.WriteLine($"异常类型：{ex.GetType().Name}");
                Console.WriteLine($"堆栈跟踪：{ex.StackTrace}");
                
                MessageBox.Show($"应用程序启动失败：\n{ex.Message}\n\n详细信息：\n{ex}",
                    "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 强制退出应用程序
                Environment.Exit(1);
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            Console.WriteLine("应用程序正在退出...");
            _host?.Dispose();
            base.OnExit(e);
        }

        private void Application_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            Console.WriteLine($"未处理的异常：{e.Exception.Message}");
            Console.WriteLine($"异常类型：{e.Exception.GetType().Name}");
            Console.WriteLine($"堆栈跟踪：{e.Exception.StackTrace}");
            
            var logger = _host?.Services.GetService<ILogger<App>>();
            logger?.LogError(e.Exception, "未处理的应用程序异常");

            MessageBox.Show($"应用程序发生未处理的异常：\n{e.Exception.Message}", 
                "错误", MessageBoxButton.OK, MessageBoxImage.Error);

            e.Handled = true;
        }
    }
}
