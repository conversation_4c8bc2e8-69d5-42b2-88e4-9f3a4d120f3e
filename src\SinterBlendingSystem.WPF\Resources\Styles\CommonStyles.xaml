<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 现代化导航按钮样式 -->
    <Style x:Key="NavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Margin" Value="4,2"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="#E0E0E0"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                            <GradientStop Color="{StaticResource PrimaryColor}" Offset="0"/>
                            <GradientStop Color="{StaticResource PrimaryLightColor}" Offset="1"/>
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="{StaticResource PrimaryColor}" Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="{StaticResource PrimaryDarkBrush}"/>
                <Setter Property="Foreground" Value="White"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 卡片样式 -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect" Value="{StaticResource CardShadow}"/>
    </Style>

    <!-- 统一字体样式 -->
    <Style x:Key="TitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="22"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
    </Style>

    <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
    </Style>

    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
    </Style>

    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
    </Style>

    <!-- 强调文本样式 -->
    <Style x:Key="EmphasisTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
    </Style>

    <!-- 现代化输入框样式 -->
    <Style x:Key="MaterialTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignFilledTextBox}">
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.HasClearButton" Value="True"/>
        <Setter Property="materialDesign:TextFieldAssist.RippleOnFocusEnabled" Value="True"/>
        <Setter Property="BorderThickness" Value="0,0,0,2"/>
    </Style>

    <!-- 数值输入框样式 -->
    <Style x:Key="NumericTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialTextBoxStyle}">
        <Setter Property="HorizontalContentAlignment" Value="Right"/>
        <Setter Property="InputScope" Value="Number"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 现代化组合框样式 -->
    <Style x:Key="MaterialComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignFilledComboBox}">
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
    </Style>

    <!-- 现代化分组框样式 -->
    <Style x:Key="MaterialGroupBoxStyle" TargetType="GroupBox" BasedOn="{StaticResource MaterialDesignCardGroupBox}">
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
        <Setter Property="materialDesign:ColorZoneAssist.Mode" Value="PrimaryLight"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp1"/>
    </Style>

    <!-- 现代化按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource PrimaryGradientBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource TextOnPrimaryBrush}"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource PrimaryColor}" Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="SecondaryButtonStyle" TargetType="Button">
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
        <Setter Property="BorderThickness" Value="2"/>
    </Style>

    <Style x:Key="AccentButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource ButtonGradientBrush}"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontFamily" Value="{DynamicResource MaterialDesignFont}"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="{StaticResource AccentColor}" Opacity="0.3" BlurRadius="8" ShadowDepth="2"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 图标按钮样式 -->
    <Style x:Key="IconButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFloatingActionMiniButton}">
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp1"/>
    </Style>



    <!-- 高级卡片样式 -->
    <Style x:Key="ElevatedCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{StaticResource CardGradientBrush}"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp3"/>
    </Style>

    <!-- 状态指示器样式 -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="Margin" Value="4,0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 工具栏样式 -->
    <Style x:Key="ToolBarStyle" TargetType="ToolBar" BasedOn="{StaticResource MaterialDesignToolBar}">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Padding" Value="8"/>
    </Style>

    <!-- 分隔符样式 -->
    <Style x:Key="SeparatorStyle" TargetType="Separator">
        <Setter Property="Background" Value="{StaticResource DividerBrush}"/>
        <Setter Property="Margin" Value="0,8"/>
        <Setter Property="Height" Value="1"/>
    </Style>

    <!-- 数据模板：状态指示器 -->
    <DataTemplate x:Key="StatusIndicatorTemplate">
        <StackPanel Orientation="Horizontal">
            <Ellipse Style="{StaticResource StatusIndicatorStyle}">
                <Ellipse.Fill>
                    <SolidColorBrush Color="Green"/>
                </Ellipse.Fill>
            </Ellipse>
            <TextBlock Text="正常" VerticalAlignment="Center" FontSize="10"/>
        </StackPanel>
    </DataTemplate>

    <!-- 数据模板：原料类型图标 -->
    <DataTemplate x:Key="MaterialTypeIconTemplate">
        <materialDesign:PackIcon Kind="Factory" Width="16" Height="16" VerticalAlignment="Center" Foreground="Blue"/>
    </DataTemplate>

    <!-- 数据模板：百分比显示 -->
    <DataTemplate x:Key="PercentageTemplate">
        <TextBlock Text="{Binding StringFormat='{}{0:F2}%'}" 
                   HorizontalAlignment="Right" 
                   VerticalAlignment="Center"/>
    </DataTemplate>

    <!-- 数据模板：货币显示 -->
    <DataTemplate x:Key="CurrencyTemplate">
        <TextBlock Text="{Binding StringFormat='{}{0:F2} 元/吨'}" 
                   HorizontalAlignment="Right" 
                   VerticalAlignment="Center"/>
    </DataTemplate>

    <!-- 控件模板：加载指示器 -->
    <ControlTemplate x:Key="LoadingIndicatorTemplate">
        <Grid>
            <materialDesign:Card Padding="32" HorizontalAlignment="Center" VerticalAlignment="Center">
                <StackPanel Orientation="Horizontal">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}" 
                                 IsIndeterminate="True" 
                                 Width="24" Height="24" 
                                 Margin="0,0,16,0"/>
                    <TextBlock Text="正在加载..." VerticalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </ControlTemplate>

    <!-- 控件模板：空数据提示 -->
    <ControlTemplate x:Key="EmptyDataTemplate">
        <Grid>
            <materialDesign:Card Padding="32" HorizontalAlignment="Center" VerticalAlignment="Center">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="DatabaseOff" Width="48" Height="48" 
                                           Foreground="{StaticResource TextSecondaryBrush}" 
                                           HorizontalAlignment="Center" Margin="0,0,0,16"/>
                    <TextBlock Text="暂无数据" Style="{StaticResource SubtitleTextStyle}" 
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="请添加原料数据或检查数据源" Style="{StaticResource CaptionTextStyle}" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </ControlTemplate>

</ResourceDictionary>
