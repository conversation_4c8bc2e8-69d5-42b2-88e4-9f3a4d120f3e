SinterBlendingSystem/
├── SinterBlendingSystem.sln                    # 解决方案文件
├── src/
│   ├── SinterBlendingSystem.WPF/              # WPF主项目
│   │   ├── Views/                              # 界面视图
│   │   │   ├── MainWindow.xaml                 # 主窗口
│   │   │   ├── MaterialManagement/            # 原料管理页面
│   │   │   │   ├── MaterialEditView.xaml      # 原料编辑页
│   │   │   │   └── MaterialListView.xaml      # 原料列表页
│   │   │   ├── OptimizationConfig/            # 优化配置页面
│   │   │   │   ├── TargetConfigView.xaml      # 目标配置页
│   │   │   │   └── ConstraintConfigView.xaml  # 约束配置页
│   │   │   └── ResultDisplay/                 # 结果展示页面
│   │   │       ├── ResultComparisonView.xaml  # 多方案对比页
│   │   │       └── ResultVisualizationView.xaml # 结果可视化页
│   │   ├── ViewModels/                         # MVVM视图模型
│   │   │   ├── MainViewModel.cs
│   │   │   ├── MaterialManagementViewModel.cs
│   │   │   ├── OptimizationConfigViewModel.cs
│   │   │   └── ResultDisplayViewModel.cs
│   │   ├── Models/                             # 数据模型
│   │   │   ├── MaterialData.cs                 # 原料数据模型
│   │   │   ├── OptimizationTarget.cs           # 优化目标模型
│   │   │   ├── ConstraintRanges.cs             # 约束范围模型
│   │   │   └── OptimizationResult.cs           # 优化结果模型
│   │   ├── Services/                           # 业务服务层
│   │   │   ├── IOptimizationService.cs         # 优化服务接口
│   │   │   ├── OptimizationService.cs          # 优化服务实现
│   │   │   ├── IDataService.cs                 # 数据服务接口
│   │   │   └── DataService.cs                  # 数据服务实现
│   │   ├── Controls/                           # 自定义控件
│   │   │   ├── MaterialDataGrid.xaml           # 原料数据表格控件
│   │   │   ├── OptimizationChart.xaml          # 优化结果图表控件
│   │   │   └── StatusBar.xaml                  # 状态栏控件
│   │   ├── Converters/                         # 值转换器
│   │   │   ├── BoolToVisibilityConverter.cs
│   │   │   └── PercentageConverter.cs
│   │   ├── Resources/                          # 资源文件
│   │   │   ├── Styles/                         # 样式文件
│   │   │   ├── Images/                         # 图片资源
│   │   │   └── Localization/                   # 本地化资源
│   │   └── App.xaml                            # 应用程序入口
│   ├── SinterBlendingSystem.Core/              # 核心业务逻辑库
│   │   ├── Models/                             # 共享数据模型
│   │   ├── Services/                           # 核心服务
│   │   ├── Interfaces/                         # 接口定义
│   │   └── Utilities/                          # 工具类
│   └── SinterBlendingSystem.Tests/             # 单元测试项目
├── python-service/                             # Python算法服务
│   ├── optimization_service.py                 # 现有SQP算法服务
│   ├── requirements.txt                        # Python依赖
│   ├── Dockerfile                              # Docker容器化
│   └── config/                                 # 配置文件
├── docs/                                       # 文档目录
│   ├── api/                                    # API文档
│   ├── user-guide/                             # 用户指南
│   └── development/                            # 开发文档
└── deployment/                                 # 部署脚本
    ├── docker-compose.yml                      # 容器编排
    └── scripts/                                # 部署脚本
