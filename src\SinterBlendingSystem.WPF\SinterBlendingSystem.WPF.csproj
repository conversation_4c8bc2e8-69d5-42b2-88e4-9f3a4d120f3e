<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>烧结配料系统</AssemblyTitle>
    <AssemblyDescription>基于SQP算法的烧结配料优化系统</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>烧结配料系统开发团队</Company>
    <Product>烧结配料系统</Product>
    <Copyright>Copyright © 2025</Copyright>

    <StartupObject>SinterBlendingSystem.WPF.App</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="7.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />

    <PackageReference Include="MaterialDesignThemes" Version="5.0.0" />
    <PackageReference Include="MaterialDesignColors" Version="3.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SinterBlendingSystem.Core\SinterBlendingSystem.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\Images\" />
    <Folder Include="Resources\Styles\" />
    <Folder Include="Resources\Localization\" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\Images\**\*" />
  </ItemGroup>

</Project>
